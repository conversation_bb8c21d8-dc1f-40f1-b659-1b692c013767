<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { onMounted, ref, shallowRef, watch } from 'vue';
import { Repos, type AccountDetail, type LegacyFundInfo } from '../../../../../xtrade-sdk/dist';
import type { ColumnDefinition } from '@/types';
import { render2Nav, render2Percentage, render2Thousands } from '@/script';
import VirtualizedTable from '../VirtualizedTable.vue';

// 定义组件属性
const { activeItem } = defineProps<{
  activeItem?: LegacyFundInfo;
}>();

const repoInstance = new Repos.GovernanceRepo();
// 概览数据
const overviewData = shallowRef<AccountDetail[]>([]);

const columns: ColumnDefinition<AccountDetail> = [
  {
    key: 'identityName',
    title: '账号名称',
    width: 200,
    sortable: true,
    searchable: true,
  },
  {
    key: 'balance',
    title: '权益',
    width: 120,
    sortable: true,
    align: 'right',
    cellRenderer: render2Thousands,
  },
  {
    key: 'marketValue',
    title: '市值',
    width: 120,
    sortable: true,
    align: 'right',
    cellRenderer: render2Thousands,
  },
  {
    key: 'available',
    title: '可用资金',
    width: 120,
    sortable: true,
    align: 'right',
    cellRenderer: render2Thousands,
  },
  {
    key: 'risePercent',
    title: '收益率',
    width: 80,
    sortable: true,
    align: 'right',
    cellRenderer: render2Percentage,
  },
  {
    key: 'positionProfit',
    title: '持仓收益',
    width: 120,
    sortable: true,
    align: 'right',
    cellRenderer: render2Thousands,
  },
  {
    key: 'nav',
    title: '净值',
    width: 70,
    sortable: true,
    align: 'right',
    cellRenderer: render2Nav,
  },
  {
    key: 'preBalance',
    title: '昨日权益',
    width: 120,
    sortable: true,
    align: 'right',
    cellRenderer: render2Thousands,
  },
  {
    key: 'frozenMargin',
    title: '冻结资金',
    width: 100,
    sortable: true,
    align: 'right',
    cellRenderer: render2Thousands,
  },
  {
    key: 'closeProfit',
    title: '平仓盈亏',
    width: 100,
    sortable: true,
    align: 'right',
    cellRenderer: render2Thousands,
  },
  {
    key: 'loanBuyBalance',
    title: '融资买入金额',
    width: 120,
    sortable: true,
    align: 'right',
    cellRenderer: render2Thousands,
  },
  {
    key: 'loanSellBalance',
    title: '可用融券卖出资金',
    width: 150,
    sortable: true,
    align: 'right',
    cellRenderer: render2Thousands,
  },
  {
    key: 'loanSellQuota',
    title: '融券卖出金额',
    width: 120,
    sortable: true,
    align: 'right',
    cellRenderer: render2Thousands,
  },
  {
    key: 'commission',
    title: '手续费',
    width: 80,
    sortable: true,
    align: 'right',
    cellRenderer: render2Thousands,
  },
  {
    key: 'margin',
    title: '占用保证金',
    width: 100,
    sortable: true,
    align: 'right',
    cellRenderer: render2Thousands,
  },
];

// 获取概览数据
const fetchOverviewData = async () => {
  if (!activeItem) return;
  const { errorCode, errorMsg, data } = await repoInstance.QueryAccountDetails(activeItem.id);
  if (errorCode == 0) {
    overviewData.value = data?.list || [];
  } else {
    console.error(errorMsg);
    overviewData.value = [];
  }
};

onMounted(() => {
  if (activeItem) {
    fetchOverviewData();
  }
});

// 监听activeItem变化
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      fetchOverviewData();
    }
  },
  { deep: true },
);
</script>

<template>
  <div h-full v-if="!activeItem" flex jcc aic h-200 text-gray-500>请选择产品</div>
  <div h-full v-else>
    <VirtualizedTable fixed :columns="columns" :data="overviewData" />
  </div>
</template>

<style scoped></style>
